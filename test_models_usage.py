#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试v6中的models文件夹是否还在使用
验证lj_env_1严格验证模型是否完全独立运行
"""

import sys
import os
from pathlib import Path

def test_models_folder_usage():
    """测试models文件夹的使用情况"""
    print("="*60)
    print("🔍 测试v6中models文件夹的使用情况")
    print("="*60)
    
    # 添加路径
    v6_dir = Path("kongwen_power_control/beta_version/v6")
    sys.path.append(str(v6_dir / "production_deployment" / "src"))
    
    try:
        # 1. 检查models文件夹内容
        models_dir = v6_dir / "production_deployment" / "models"
        print(f"📁 models文件夹路径: {models_dir}")
        
        if models_dir.exists():
            model_files = list(models_dir.rglob("*"))
            print(f"📋 models文件夹内容 ({len(model_files)}个文件):")
            for file_path in sorted(model_files):
                if file_path.is_file():
                    size_mb = file_path.stat().st_size / (1024 * 1024)
                    print(f"  - {file_path.name}: {size_mb:.2f}MB")
        else:
            print("❌ models文件夹不存在")
            return False
        
        # 2. 测试预测器是否还加载models文件
        print(f"\n🧪 测试预测器模型加载...")
        
        # 临时重命名models文件夹，测试是否影响预测
        backup_dir = models_dir.parent / "models_backup_test"
        
        try:
            # 重命名models文件夹
            if models_dir.exists() and not backup_dir.exists():
                models_dir.rename(backup_dir)
                print(f"✅ 临时重命名models文件夹为: {backup_dir}")
            
            # 尝试导入和使用预测器
            from predict import VicePowerPredictor
            
            print(f"📦 导入VicePowerPredictor...")
            predictor = VicePowerPredictor()
            print(f"✅ 预测器初始化成功 (无models文件夹)")
            
            # 测试预测功能
            result = predictor.predict_single(200, 150, '首投')
            predicted_power = result.get('predicted_vice_power_kwh')
            model_used = result.get('model_used', 'Unknown')
            
            if predicted_power is not None:
                print(f"✅ 预测功能正常: {predicted_power:.2f}kWh (模型: {model_used})")
                models_independent = True
            else:
                print(f"❌ 预测功能失败: {result}")
                models_independent = False
            
        except Exception as e:
            print(f"❌ 无models文件夹时预测器失败: {e}")
            models_independent = False
        
        finally:
            # 恢复models文件夹
            if backup_dir.exists():
                backup_dir.rename(models_dir)
                print(f"✅ 恢复models文件夹")
        
        # 3. 测试有models文件夹时的情况
        print(f"\n🧪 测试有models文件夹时的预测...")
        
        try:
            # 重新导入（清除缓存）
            if 'predict' in sys.modules:
                del sys.modules['predict']
            
            from predict import VicePowerPredictor
            
            predictor_with_models = VicePowerPredictor()
            result_with_models = predictor_with_models.predict_single(200, 150, '首投')
            predicted_power_with_models = result_with_models.get('predicted_vice_power_kwh')
            model_used_with_models = result_with_models.get('model_used', 'Unknown')
            
            if predicted_power_with_models is not None:
                print(f"✅ 有models文件夹时预测正常: {predicted_power_with_models:.2f}kWh (模型: {model_used_with_models})")
                
                # 比较两种情况的结果
                if models_independent:
                    diff = abs(predicted_power - predicted_power_with_models)
                    print(f"📊 预测结果对比:")
                    print(f"  无models文件夹: {predicted_power:.2f}kWh")
                    print(f"  有models文件夹: {predicted_power_with_models:.2f}kWh")
                    print(f"  差异: {diff:.2f}kWh")
                    
                    if diff < 0.01:
                        print(f"✅ 预测结果完全一致 - models文件夹未被使用")
                        models_unused = True
                    else:
                        print(f"⚠️ 预测结果有差异 - models文件夹可能仍在使用")
                        models_unused = False
                else:
                    models_unused = False
            else:
                print(f"❌ 有models文件夹时预测也失败")
                models_unused = False
                
        except Exception as e:
            print(f"❌ 有models文件夹时预测器失败: {e}")
            models_unused = False
        
        # 4. 分析结果
        print(f"\n" + "="*60)
        print("📊 models文件夹使用情况分析")
        print("="*60)
        
        if models_independent and models_unused:
            print("✅ models文件夹未被使用")
            print("✅ lj_env_1严格验证模型完全独立运行")
            print("✅ 可以安全删除models文件夹中的复杂模型文件")
            
            # 计算可节省的空间
            total_size = 0
            for file_path in models_dir.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            
            size_mb = total_size / (1024 * 1024)
            print(f"💾 可节省空间: {size_mb:.2f}MB")
            
            recommendation = "SAFE_TO_REMOVE"
            
        elif models_independent and not models_unused:
            print("⚠️ models文件夹部分使用")
            print("⚠️ lj_env_1模型可独立运行，但结果有差异")
            print("⚠️ 建议保留models文件夹作为备份")
            recommendation = "KEEP_AS_BACKUP"
            
        else:
            print("❌ models文件夹仍在使用")
            print("❌ lj_env_1模型依赖复杂模型文件")
            print("❌ 不能删除models文件夹")
            recommendation = "MUST_KEEP"
        
        # 5. 生成建议
        print(f"\n🎯 建议:")
        
        if recommendation == "SAFE_TO_REMOVE":
            print("1. ✅ 可以安全删除models文件夹中的复杂模型文件")
            print("2. ✅ 保留models文件夹结构，只保留必要的配置文件")
            print("3. ✅ lj_env_1严格验证模型已完全替代复杂模型")
            
        elif recommendation == "KEEP_AS_BACKUP":
            print("1. ⚠️ 暂时保留models文件夹作为备份")
            print("2. ⚠️ 进一步调试预测结果差异的原因")
            print("3. ⚠️ 确认lj_env_1模型完全独立后再删除")
            
        else:
            print("1. ❌ 必须保留models文件夹")
            print("2. ❌ 需要进一步完善lj_env_1模型集成")
            print("3. ❌ 检查代码中是否还有对复杂模型的依赖")
        
        return {
            'models_independent': models_independent,
            'models_unused': models_unused,
            'recommendation': recommendation,
            'total_size_mb': size_mb if 'size_mb' in locals() else 0
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {
            'models_independent': False,
            'models_unused': False,
            'recommendation': 'TEST_FAILED',
            'total_size_mb': 0
        }

if __name__ == "__main__":
    result = test_models_usage()
    
    print(f"\n🎉 测试完成!")
    print(f"📊 结果: {result['recommendation']}")
    
    if result['recommendation'] == 'SAFE_TO_REMOVE':
        print(f"💾 可节省: {result['total_size_mb']:.2f}MB")
        print(f"🚀 lj_env_1严格验证模型完全独立运行!")
