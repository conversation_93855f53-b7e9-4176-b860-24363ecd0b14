# v6副功率预测模型 - 现场部署版本

## 📁 文件结构

### 核心文件
- **`model.py`** - 主模型文件，包含KongwenGonglvCorrectionModel类
- **`call.py`** - API接口文件，提供setup/predict/finish接口
- **`dynamicheating.py`** - 动态加热功能模块

### 配置文件
- **`model_data/config.yaml`** - 模型配置参数
- **`model_data/dynamicheating.xlsx`** - 动态加热数据

### 副功率预测系统
- **`production_deployment/`** - 副功率预测系统
  - `src/predict.py` - lj_env_1严格验证预测器
  - `src/__init__.py` - 模块初始化
  - `models/` - 模型文件目录

## 🚀 使用说明

### 1. 模型初始化
```python
from model import KongwenGonglvCorrectionModel

# 创建模型实例
model = KongwenGonglvCorrectionModel.from_path('model_data/config.yaml')

# 设置模型参数
model.setup(
    device_id="A3600",
    jialiao=587.0,
    times=[],
    power_yinjing=54.5,
    init_power=(100.0, 80.0),
    config={},
    field_size="36",
    product_type="11",
    target_ccd=1448.0,
    history_data=[],
    feeding_type=1
)
```

### 2. 实时预测
```python
# 调用预测方法
main_power, vice_power, vice_power_info = model.predict(
    t=4694.0,
    ratio=99.9968,
    ccd=-1.0,
    ccd3=1469.3,
    fullmelting=1,
    # ... 其他参数
)

# 解析结果
real_time_vice_power, current_cumulative_power, predicted_total_power = vice_power_info
```

### 3. API接口使用
```python
from call import (
    kongwen_realtime_powerfix_test_setup,
    kongwen_realtime_powerfix_test
)

# 使用API接口进行调用
```

## ⚡ 副功率预测特性

- **高精度**: 84.9%的±10kWh准确率
- **实时计算**: 支持实时累积功率计算
- **智能控制**: 自动关闭机制
- **稳定可靠**: 多层降级机制

## 📊 核心算法

### lj_env_1严格验证模型
```
predicted_power = 19.85 + 0.342 * weight_difference + 1.287 * silicon_thermal_energy_kwh
```

### 状态机控制
```
INIT → VICE_CLOSE1 → VICE_CLOSE2 → ALMOST_DONE → DONE
```

## 🔧 配置参数

主要配置参数在`model_data/config.yaml`中：
- `vice_close_ratio`: 副功率关闭比例
- `high_ratio`: 高溶液比阈值
- `time_range`: 时间范围参数
- `done_power_k`: 完成阶段功率系数

## 📝 注意事项

1. 确保所有依赖模块已正确安装
2. 配置文件路径正确
3. 输入参数格式符合要求
4. 定期检查预测精度

## 🆔 版本信息

- **版本**: v6 现场部署版
- **副功率预测器**: lj_env_1严格验证模型
- **准确率**: 84.9% (±10kWh)
- **更新日期**: 2025-07-31
